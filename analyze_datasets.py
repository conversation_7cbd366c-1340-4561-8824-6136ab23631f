#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据集分析脚本
"""

import pandas as pd
import os
import sys

def analyze_datasets():
    """分析所有数据集"""
    data_dir = '数据可视化数据集-A'
    
    if not os.path.exists(data_dir):
        print(f"数据目录 {data_dir} 不存在")
        return
    
    files = [f for f in os.listdir(data_dir) if f.endswith('.xlsx')]
    
    print(f"找到 {len(files)} 个Excel文件:")
    for file in files:
        print(f"- {file}")
    
    print("\n" + "="*50)
    
    for file in files:
        print(f'\n=== {file} ===')
        try:
            file_path = os.path.join(data_dir, file)
            df = pd.read_excel(file_path)
            
            print(f'数据形状: {df.shape}')
            print(f'列名: {list(df.columns)}')
            print('\n前5行数据:')
            print(df.head())
            print('\n数据类型:')
            print(df.dtypes)
            print('\n缺失值统计:')
            print(df.isnull().sum())
            
            # 数值列的基本统计
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                print('\n数值列基本统计:')
                print(df[numeric_cols].describe())
            
        except Exception as e:
            print(f'读取错误: {e}')
        
        print("\n" + "-"*30)

if __name__ == "__main__":
    analyze_datasets()
