#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
数据可视化期末考核任务
作者：学生
日期：2025年5月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置暖色调配色方案
WARM_COLORS = ['#FF6B6B', '#FFE66D', '#FF8E53', '#FF6B9D', '#C44569', '#F8B500', '#FF7675', '#FDCB6E']
WARM_PALETTE = sns.color_palette(WARM_COLORS)

def check_chinese_fonts():
    """检查可用的中文字体"""
    import matplotlib.font_manager as fm

    chinese_fonts = []
    for font in fm.fontManager.ttflist:
        if 'SimHei' in font.name or 'Microsoft YaHei' in font.name or 'SimSun' in font.name:
            chinese_fonts.append(font.name)

    print("可用的中文字体:")
    for font in set(chinese_fonts):
        print(f"- {font}")

    return chinese_fonts

def load_datasets():
    """加载数据集"""
    datasets = {}

    # 加载二手房数据
    datasets['house'] = pd.read_excel('数据可视化数据集-A/二手房数据.xlsx')

    # 加载营销数据
    datasets['marketing'] = pd.read_excel('数据可视化数据集-A/营销和产品销售表.xlsx')

    # 加载GDP数据
    datasets['gdp'] = pd.read_excel('数据可视化数据集-A/国内生产总值季度数据.xlsx')

    return datasets

def create_house_visualizations(df):
    """创建二手房数据可视化"""
    print("正在生成二手房数据可视化图表...")

    # 1. 散点图：面积 vs 单价
    plt.figure(figsize=(12, 8))
    districts = df['所在区'].unique()
    colors = WARM_COLORS * (len(districts) // len(WARM_COLORS) + 1)  # 确保有足够的颜色

    for i, district in enumerate(districts):
        district_data = df[df['所在区'] == district]
        plt.scatter(district_data['面积（平方米）'], district_data['单价（元/平方米）'],
                   c=colors[i], label=district, alpha=0.7, s=50)

    plt.xlabel('面积（平方米）', fontsize=14, fontweight='bold')
    plt.ylabel('单价（元/平方米）', fontsize=14, fontweight='bold')
    plt.title('北京各区二手房面积与单价关系散点图', fontsize=16, fontweight='bold', pad=20)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图表1_二手房面积单价散点图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 箱形图：各区域房价分布
    plt.figure(figsize=(14, 8))
    box_plot = plt.boxplot([df[df['所在区'] == district]['单价（元/平方米）'] for district in districts],
                          labels=districts, patch_artist=True)

    for patch, color in zip(box_plot['boxes'], colors):
        patch.set_facecolor(color)
        patch.set_alpha(0.7)

    plt.xlabel('区域', fontsize=14, fontweight='bold')
    plt.ylabel('单价（元/平方米）', fontsize=14, fontweight='bold')
    plt.title('北京各区二手房单价分布箱形图', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图表2_各区房价分布箱形图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 条形图：各区域平均房价
    avg_price = df.groupby('所在区')['单价（元/平方米）'].mean().sort_values(ascending=False)

    plt.figure(figsize=(12, 8))
    bars = plt.bar(avg_price.index, avg_price.values, color=colors[:len(avg_price)])

    # 添加数值标签
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 1000,
                f'{height:.0f}', ha='center', va='bottom', fontweight='bold')

    plt.xlabel('区域', fontsize=14, fontweight='bold')
    plt.ylabel('平均单价（元/平方米）', fontsize=14, fontweight='bold')
    plt.title('北京各区二手房平均单价排名', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('图表3_各区平均房价条形图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_marketing_visualizations(df):
    """创建营销数据可视化"""
    print("正在生成营销数据可视化图表...")

    # 4. 折线图：营销费用与订单金额趋势
    fig, ax1 = plt.subplots(figsize=(14, 8))

    ax1.plot(df['日期'], df['营销费用（元）'], marker='o', linewidth=3,
             color=WARM_COLORS[0], label='营销费用', markersize=6)
    ax1.set_xlabel('日期', fontsize=14, fontweight='bold')
    ax1.set_ylabel('营销费用（元）', fontsize=14, fontweight='bold', color=WARM_COLORS[0])
    ax1.tick_params(axis='y', labelcolor=WARM_COLORS[0])

    ax2 = ax1.twinx()
    ax2.plot(df['日期'], df['订单金额（元）'], marker='s', linewidth=3,
             color=WARM_COLORS[1], label='订单金额', markersize=6)
    ax2.set_ylabel('订单金额（元）', fontsize=14, fontweight='bold', color=WARM_COLORS[1])
    ax2.tick_params(axis='y', labelcolor=WARM_COLORS[1])

    plt.title('营销费用与订单金额时间趋势图', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(rotation=45)
    ax1.grid(True, alpha=0.3)

    # 添加图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

    plt.tight_layout()
    plt.savefig('图表4_营销费用订单金额趋势图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 5. 气泡图：展现量、点击量、订单金额三维关系
    plt.figure(figsize=(12, 8))

    # 使用订单金额作为气泡大小
    bubble_sizes = (df['订单金额（元）'] / df['订单金额（元）'].max() * 1000)

    scatter = plt.scatter(df['展现量'], df['点击量'], s=bubble_sizes,
                         c=df['订单金额（元）'], cmap='YlOrRd', alpha=0.7, edgecolors='black')

    plt.xlabel('展现量', fontsize=14, fontweight='bold')
    plt.ylabel('点击量', fontsize=14, fontweight='bold')
    plt.title('营销效果三维关系气泡图\n（气泡大小和颜色代表订单金额）', fontsize=16, fontweight='bold', pad=20)

    # 添加颜色条
    cbar = plt.colorbar(scatter)
    cbar.set_label('订单金额（元）', fontsize=12, fontweight='bold')

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图表5_营销效果气泡图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 6. 雷达图：营销指标综合表现
    # 选择关键指标并标准化
    metrics = ['展现量', '点击量', '订单金额（元）', '加购数', '下单新客数', '进店数']

    # 计算平均值并标准化到0-1范围
    avg_values = []
    for metric in metrics:
        normalized_value = (df[metric].mean() - df[metric].min()) / (df[metric].max() - df[metric].min())
        avg_values.append(normalized_value)

    # 创建雷达图
    angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
    avg_values += avg_values[:1]  # 闭合图形
    angles += angles[:1]

    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    ax.plot(angles, avg_values, 'o-', linewidth=3, color=WARM_COLORS[2], markersize=8)
    ax.fill(angles, avg_values, alpha=0.25, color=WARM_COLORS[2])

    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics, fontsize=12, fontweight='bold')
    ax.set_ylim(0, 1)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'])
    ax.grid(True)

    plt.title('营销指标综合表现雷达图', fontsize=16, fontweight='bold', pad=30)
    plt.tight_layout()
    plt.savefig('图表6_营销指标雷达图.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_gdp_visualizations(df):
    """创建GDP数据可视化"""
    print("正在生成GDP数据可视化图表...")

    # 数据预处理：转换为长格式
    quarters = [col for col in df.columns if '年第' in col]
    quarters.reverse()  # 按时间顺序排列

    # 7. 面积图：各产业GDP构成的时间变化
    plt.figure(figsize=(16, 10))

    # 提取三大产业数据
    primary_data = df[df['指标'] == '第一产业增加值（亿元）'][quarters].values[0]
    secondary_data = df[df['指标'] == '第二产业增加值（亿元）'][quarters].values[0]
    tertiary_data = df[df['指标'] == '第三产业增加值（亿元）'][quarters].values[0]

    # 创建面积图
    plt.stackplot(range(len(quarters)), primary_data, secondary_data, tertiary_data,
                 labels=['第一产业', '第二产业', '第三产业'],
                 colors=[WARM_COLORS[0], WARM_COLORS[1], WARM_COLORS[2]], alpha=0.8)

    plt.xlabel('季度', fontsize=14, fontweight='bold')
    plt.ylabel('增加值（亿元）', fontsize=14, fontweight='bold')
    plt.title('中国三大产业GDP构成时间变化面积图', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(range(len(quarters)), [q.replace('年第', 'Q').replace('季度', '') for q in quarters], rotation=45)
    plt.legend(loc='upper left', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('图表7_三大产业GDP面积图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 8. 簇状柱形图：各季度三大产业对比
    plt.figure(figsize=(16, 10))

    x = np.arange(len(quarters))
    width = 0.25

    bars1 = plt.bar(x - width, primary_data, width, label='第一产业', color=WARM_COLORS[0], alpha=0.8)
    bars2 = plt.bar(x, secondary_data, width, label='第二产业', color=WARM_COLORS[1], alpha=0.8)
    bars3 = plt.bar(x + width, tertiary_data, width, label='第三产业', color=WARM_COLORS[2], alpha=0.8)

    plt.xlabel('季度', fontsize=14, fontweight='bold')
    plt.ylabel('增加值（亿元）', fontsize=14, fontweight='bold')
    plt.title('中国三大产业GDP季度对比簇状柱形图', fontsize=16, fontweight='bold', pad=20)
    plt.xticks(x, [q.replace('年第', 'Q').replace('季度', '') for q in quarters], rotation=45)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig('图表8_三大产业GDP簇状柱形图.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 9. 热力图：各指标季度变化热力图
    plt.figure(figsize=(16, 8))

    # 准备热力图数据
    heatmap_data = df.set_index('指标')[quarters]

    # 标准化数据以便比较
    heatmap_data_normalized = heatmap_data.div(heatmap_data.max(axis=1), axis=0)

    sns.heatmap(heatmap_data_normalized, annot=False, cmap='YlOrRd',
                cbar_kws={'label': '相对值（标准化）'}, linewidths=0.5)

    plt.xlabel('季度', fontsize=14, fontweight='bold')
    plt.ylabel('经济指标', fontsize=14, fontweight='bold')
    plt.title('中国GDP各指标季度变化热力图', fontsize=16, fontweight='bold', pad=20)
    plt.xticks([i+0.5 for i in range(len(quarters))],
               [q.replace('年第', 'Q').replace('季度', '') for q in quarters], rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig('图表9_GDP指标热力图.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    # 检查中文字体
    check_chinese_fonts()

    # 加载数据
    datasets = load_datasets()

    # 生成可视化图表
    create_house_visualizations(datasets['house'])
    create_marketing_visualizations(datasets['marketing'])
    create_gdp_visualizations(datasets['gdp'])

    print("所有9张图表生成完成！")
